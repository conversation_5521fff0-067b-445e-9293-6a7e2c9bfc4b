<?php
/**
 * Stenik Sync catalog product source model
 *
 * @package Stenik_Sync
 * <AUTHOR> Magento Team <<EMAIL>>
 */

class Stenik_Sync_Model_Source_Catalog_Product implements Stenik_Sync_Model_Interface_Product
{
    /**
     * Get collection of products
     *
     * @param Zend_Date $since
     * @return array
     */
    public function getCollection(Zend_Date $since = null)
    {
        // This method would typically return a collection of products to sync
        // For now, return empty array as this is mainly used for bulk operations
        return array();
    }

    /**
     * Get item data for a specific product
     *
     * @param Mage_Catalog_Model_Product $product
     * @return Varien_Object|null
     */
    public function getItem(Mage_Catalog_Model_Product $product)
    {
        // This method should return the data to sync for a specific product
        // For now, return a basic object with the product data
        // This can be extended based on specific sync requirements
        
        $item = new Varien_Object();
        $item->setMagentoId($product->getId());
        $item->setMagentoSku($product->getSku());
        $item->setTypeId($product->getTypeId());
        $item->setName($product->getName());
        $item->setPrice($product->getPrice());
        $item->setStatus($product->getStatus());
        
        return $item;
    }
}
