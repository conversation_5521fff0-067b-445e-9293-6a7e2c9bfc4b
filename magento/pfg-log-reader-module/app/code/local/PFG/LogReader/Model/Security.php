<?php
/**
 * PFG Log Reader Security Model
 *
 * @category   PFG
 * @package    PFG_LogReader
 * <AUTHOR> Development Team
 */

/**
 * Security Model
 *
 * Provides additional security validation and monitoring
 * for log file access operations.
 *
 * @category   PFG
 * @package    PFG_LogReader
 * <AUTHOR> Development Team
 * @version    1.0.0
 * @since      1.0.0
 */
class PFG_LogReader_Model_Security extends Mage_Core_Model_Abstract
{
    /**
     * Helper instance
     *
     * @var PFG_LogReader_Helper_Data
     */
    protected $_helper;

    /**
     * Constructor
     */
    protected function _construct()
    {
        $this->_helper = Mage::helper('pfg_logreader');
    }

    /**
     * Validate file access permissions
     *
     * @param string $filePath
     * @return array
     */
    public function validateFileAccess($filePath)
    {
        $result = array(
            'allowed' => false,
            'reason' => '',
            'file_info' => array()
        );

        try {
            // Check if file exists
            if (!file_exists($filePath)) {
                $result['reason'] = 'File does not exist';
                return $result;
            }

            // Get real path to prevent directory traversal
            $realPath = realpath($filePath);
            if ($realPath === false) {
                $result['reason'] = 'Invalid file path';
                return $result;
            }

            // Check if path is within allowed directories
            if (!$this->_isPathInAllowedDirectory($realPath)) {
                $result['reason'] = 'File is outside allowed directories';
                $this->_logSecurityViolation('Path traversal attempt', $filePath, $realPath);
                return $result;
            }

            // Check file permissions
            if (!is_readable($realPath)) {
                $result['reason'] = 'File is not readable';
                return $result;
            }

            // Check file size
            $fileSize = filesize($realPath);
            if ($fileSize === false) {
                $result['reason'] = 'Cannot determine file size';
                return $result;
            }

            if ($fileSize > $this->_helper->getMaxFileSize()) {
                $result['reason'] = 'File exceeds maximum size limit';
                return $result;
            }

            // Check if file is a valid log file
            if (!$this->_isValidLogFile($realPath)) {
                $result['reason'] = 'File type not allowed';
                $this->_logSecurityViolation('Invalid file type access attempt', $filePath, $realPath);
                return $result;
            }

            // All checks passed
            $result['allowed'] = true;
            $result['file_info'] = array(
                'path' => $realPath,
                'size' => $fileSize,
                'modified' => filemtime($realPath),
                'type' => $this->_getFileType($realPath)
            );

        } catch (Exception $e) {
            $result['reason'] = 'Security validation error: ' . $e->getMessage();
            $this->_helper->logError('Security validation failed for file: ' . $filePath, $e);
        }

        return $result;
    }

    /**
     * Check if path is within allowed directories
     *
     * @param string $realPath
     * @return bool
     */
    protected function _isPathInAllowedDirectory($realPath)
    {
        $logDir = realpath($this->_helper->getLogDirectory());
        $reportDir = realpath($this->_helper->getReportDirectory());

        if ($logDir === false || $reportDir === false) {
            return false;
        }

        return (strpos($realPath, $logDir . DIRECTORY_SEPARATOR) === 0) || 
               (strpos($realPath, $reportDir . DIRECTORY_SEPARATOR) === 0);
    }

    /**
     * Check if file is a valid log file
     *
     * @param string $filePath
     * @return bool
     */
    protected function _isValidLogFile($filePath)
    {
        $filename = basename($filePath);
        
        // Skip hidden files
        if (substr($filename, 0, 1) === '.') {
            return false;
        }

        // Check for dangerous file extensions
        $dangerousExtensions = array('php', 'phtml', 'exe', 'sh', 'bat', 'cmd');
        $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
        
        if (in_array($extension, $dangerousExtensions)) {
            return false;
        }

        // Allow common log file extensions
        $allowedExtensions = array('log', 'txt', 'out', 'err', '');
        if (in_array($extension, $allowedExtensions)) {
            return true;
        }

        // Check for log-like patterns in filename
        $logPatterns = array('error', 'access', 'debug', 'system', 'exception', 'trace');
        foreach ($logPatterns as $pattern) {
            if (stripos($filename, $pattern) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get file type based on location and name
     *
     * @param string $filePath
     * @return string
     */
    protected function _getFileType($filePath)
    {
        $logDir = realpath($this->_helper->getLogDirectory());
        $reportDir = realpath($this->_helper->getReportDirectory());

        if (strpos($filePath, $logDir) === 0) {
            return 'log';
        } elseif (strpos($filePath, $reportDir) === 0) {
            return 'report';
        }

        return 'unknown';
    }

    /**
     * Log security violation
     *
     * @param string $violation
     * @param string $requestedPath
     * @param string $realPath
     */
    protected function _logSecurityViolation($violation, $requestedPath, $realPath = null)
    {
        $adminUser = Mage::getSingleton('admin/session')->getUser();
        $username = $adminUser ? $adminUser->getUsername() : 'unknown';
        $ip = Mage::helper('core/http')->getRemoteAddr();

        $message = sprintf(
            'SECURITY VIOLATION: %s | User: %s | IP: %s | Requested: %s | Real: %s',
            $violation,
            $username,
            $ip,
            $requestedPath,
            $realPath ?: 'N/A'
        );

        $this->_helper->log($message, Zend_Log::WARN, true);
    }

    /**
     * Rate limiting check for file access
     *
     * @param string $identifier
     * @return bool
     */
    public function checkRateLimit($identifier = null)
    {
        if ($identifier === null) {
            $adminUser = Mage::getSingleton('admin/session')->getUser();
            $identifier = $adminUser ? $adminUser->getId() : Mage::helper('core/http')->getRemoteAddr();
        }

        $cacheKey = 'pfg_logreader_rate_limit_' . md5($identifier);
        $cache = Mage::app()->getCache();
        
        $requests = $cache->load($cacheKey);
        if ($requests === false) {
            $requests = 0;
        } else {
            $requests = (int)$requests; // Convert from string back to integer
        }

        $maxRequests = 100; // Max requests per minute
        $timeWindow = 60; // 1 minute

        if ($requests >= $maxRequests) {
            $this->_helper->log("Rate limit exceeded for identifier: {$identifier}");
            return false;
        }

        $cache->save((string)($requests + 1), $cacheKey, array(), $timeWindow);
        return true;
    }
}
